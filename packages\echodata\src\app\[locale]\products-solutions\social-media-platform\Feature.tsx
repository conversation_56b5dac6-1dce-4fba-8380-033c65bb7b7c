'use client';

import Union from '@hi7/assets/background/m-vector2.svg?url';
import videoPinkGif from '@hi7/assets/gif/video-pink.gif';
import clsx from 'clsx';

import AnimationFrame from '@hi7/components/AnimationFrame';
import type { DictionaryProps } from '@hi7/interface/i18n';
import type { Locale } from '@hi7/lib/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';

interface FeatureProps extends DictionaryProps {
  locale: Locale;
  isActive?: boolean;
  isExiting?: boolean;
}

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function Feature({
  dictionary,
  locale,
  isActive = true,
  isExiting = false,
}: FeatureProps) {
  const TAGS = dictionary.productSolution.socialMediaPlatforms.feature.tags;

  console.log('Feature.tsx props:', { isActive, isExiting });

  return (
    <div className="lg:rounded-bl-0 lg:rounded-tl-0 relative mt-[-3vh] h-[90vh] w-full overflow-visible rounded-tl-[2vh] rounded-br-[30px] rounded-bl-[30px] bg-[#f3dde1] lg:mt-0 lg:h-screen lg:overflow-hidden lg:rounded-tr-[10vw] lg:rounded-br-[10vw]">
      <Image
        src={Union}
        alt="video-pink"
        className="absolute top-[-9.9vh] block h-auto w-full object-cover lg:hidden"
        priority
      />
      <AnimationFrame
        variant="FadeIn"
        exitVariant="SlideDownExit"
        once={false}
        isActive={isActive}
        isExiting={isExiting}
        className="mx-auto flex w-[90vw] flex-col-reverse justify-between lg:flex-row"
      >
        <div className="relative mt-10 h-[35vh] w-full lg:mt-0 lg:h-screen lg:w-[36vw]">
          <Image
            src={videoPinkGif}
            alt="video-pink"
            className="h-full w-full object-cover object-center"
            priority
          />
        </div>
        <div className="flex w-full flex-col items-end lg:mt-20 lg:w-[60vw]">
          <div
            className={clsx(
              'z-1 w-full border-b border-[#04227D] pb-6 text-right text-[4.5dvh] leading-[110%] font-semibold whitespace-pre-line text-[#04227D] lg:pb-3 lg:text-[4vw]',
              locale === 'en' ? 'lg:w-[47vw]' : 'lg:w-[32vw]',
              arsenal.className,
            )}
          >
            {dictionary.productSolution.socialMediaPlatforms.feature.title}
          </div>
          <div
            className={clsx(
              'mt-5 flex w-full flex-wrap justify-end gap-2 lg:mt-8 lg:gap-4 lg:font-semibold',
              locale === 'en' ? 'lg:w-[45vw]' : 'lg:w-[35vw]',
            )}
          >
            {TAGS.map((tag, index) => (
              <div
                key={index}
                className="flex h-[4.5vh] items-center rounded-full bg-[#04227D] px-3 text-[3.5dvw] text-[#E9F3FF] lg:h-[7vh] lg:px-5 lg:text-[1.5vw]"
              >
                {tag}
              </div>
            ))}
            {locale === 'en' && (
              <div className="flex h-[4.5vh] items-center rounded-full bg-[#04227D] px-3 text-[4dvw] text-[#E9F3FF] lg:h-[7vh] lg:px-5 lg:text-[1.5vw]">
                Hot/Cold Account Detection&nbsp;
                <span className="font-thin"> (exclusive feature) </span>
              </div>
            )}
          </div>
        </div>
      </AnimationFrame>
    </div>
  );
}

export default Feature;

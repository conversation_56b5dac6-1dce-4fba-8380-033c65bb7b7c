'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import type React from 'react';
import { useEffect, useRef } from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  exitVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
  isActive?: boolean;
  isExiting?: boolean;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  exitVariant,
  children,
  className,
  once = true,
  margin,
  isActive = false,
  isExiting = false,
}) => {
  const { isMobile } = useScreenSize();
  const elementRef = useRef<HTMLDivElement>(null);
  const { initial, animate, transition } = defaultAnimateConfig[variant];
  const exitConfig = exitVariant ? defaultAnimateConfig[exitVariant] : null;

  useEffect(() => {
    if (isMobile || !elementRef.current) return;

    const element = elementRef.current;

    console.log('AnimationFrame: Triggering animation', {
      variant,
      exitVariant,
      isActive,
      isExiting,
    });

    if (isExiting && exitConfig) {
      // Trigger exit animation using CSS transitions
      console.log('Playing exit animation:', exitVariant, exitConfig);

      // Set transition
      const transitionValue = `transform ${exitConfig.transition.duration || 0.8}s ${exitConfig.transition.ease || 'ease-in-out'} ${exitConfig.transition.delay || 0}s, opacity ${exitConfig.transition.duration || 0.8}s ${exitConfig.transition.ease || 'ease-in-out'} ${exitConfig.transition.delay || 0}s`;
      element.style.transition = transitionValue;

      // Apply animation
      const transformValue = `translateY(${exitConfig.animate.y || 0}px)`;
      const opacityValue = String(exitConfig.animate.opacity || 1);

      element.style.transform = transformValue;
      element.style.opacity = opacityValue;

      console.log('Exit animation applied:', {
        transition: transitionValue,
        transform: transformValue,
        opacity: opacityValue,
        computedStyle: window.getComputedStyle(element).transform,
      });
    } else if (isActive) {
      // Trigger enter animation using CSS transitions
      console.log('Playing enter animation:', variant, {
        initial,
        animate,
        transition,
      });

      // Set initial state first
      const initialTransform = `translateY(${initial.y || 0}px)`;
      const initialOpacity = String(initial.opacity || 1);

      element.style.transform = initialTransform;
      element.style.opacity = initialOpacity;
      element.style.transition = 'none';

      console.log('Initial state set:', {
        transform: initialTransform,
        opacity: initialOpacity,
      });

      // Force reflow
      element.offsetHeight;

      // Set transition
      const transitionValue = `transform ${transition.duration || 0.8}s ${transition.ease || 'ease-in-out'} ${transition.delay || 0}s, opacity ${transition.duration || 0.8}s ${transition.ease || 'ease-in-out'} ${transition.delay || 0}s`;
      element.style.transition = transitionValue;

      // Apply animation
      const finalTransform = `translateY(${animate.y || 0}px)`;
      const finalOpacity = String(animate.opacity || 1);

      element.style.transform = finalTransform;
      element.style.opacity = finalOpacity;

      console.log('Enter animation applied:', {
        transition: transitionValue,
        transform: finalTransform,
        opacity: finalOpacity,
        computedStyle: window.getComputedStyle(element).transform,
      });
    }
  }, [
    isActive,
    isExiting,
    variant,
    exitVariant,
    isMobile,
    initial,
    animate,
    transition,
    exitConfig,
  ]);

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <div
      ref={elementRef}
      className={clsx('hidden lg:flex', className)}
      style={{
        transform: `translateY(${initial.y || 0}px)`,
        opacity: Number(initial.opacity || 1),
      }}
    >
      {children}
    </div>
  );
};

export default AnimationFrame;

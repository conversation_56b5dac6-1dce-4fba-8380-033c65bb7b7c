'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import type React from 'react';
import { useEffect, useRef } from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  exitVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
  isActive?: boolean;
  isExiting?: boolean;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  exitVariant,
  children,
  className,
  once = true,
  margin,
  isActive = false,
  isExiting = false,
}) => {
  const { isMobile } = useScreenSize();
  const elementRef = useRef<HTMLDivElement>(null);
  const { initial, animate, transition } = defaultAnimateConfig[variant];
  const exitConfig = exitVariant ? defaultAnimateConfig[exitVariant] : null;

  useEffect(() => {
    if (isMobile || !elementRef.current) return;

    const element = elementRef.current;

    console.log('AnimationFrame: Triggering animation', {
      variant,
      exitVariant,
      isActive,
      isExiting,
    });

    if (isExiting && exitConfig) {
      // Trigger exit animation
      console.log('Playing exit animation:', exitVariant);
      element.animate(
        [
          {
            transform: `translateY(${exitConfig.initial.y || 0}px)`,
            opacity: String(exitConfig.initial.opacity || 1),
          } as Keyframe,
          {
            transform: `translateY(${exitConfig.animate.y || 0}px)`,
            opacity: String(exitConfig.animate.opacity || 1),
          } as Keyframe,
        ],
        {
          duration: ((exitConfig.transition as any).duration || 0.8) * 1000,
          easing: 'ease-in-out',
          fill: 'forwards' as FillMode,
        },
      );
    } else if (isActive) {
      // Trigger enter animation
      console.log('Playing enter animation:', variant);
      element.animate(
        [
          {
            transform: `translateY(${initial.y || 0}px)`,
            opacity: String(initial.opacity || 1),
          } as Keyframe,
          {
            transform: `translateY(${animate.y || 0}px)`,
            opacity: String(animate.opacity || 1),
          } as Keyframe,
        ],
        {
          duration: ((transition as any).duration || 0.8) * 1000,
          easing: 'ease-in-out',
          fill: 'forwards' as FillMode,
        },
      );
    }
  }, [
    isActive,
    isExiting,
    variant,
    exitVariant,
    isMobile,
    initial,
    animate,
    transition,
    exitConfig,
  ]);

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <div
      ref={elementRef}
      className={clsx('hidden lg:flex', className)}
      style={{
        transform: `translateY(${initial.y || 0}px)`,
        opacity: Number(initial.opacity || 1),
      }}
    >
      {children}
    </div>
  );
};

export default AnimationFrame;

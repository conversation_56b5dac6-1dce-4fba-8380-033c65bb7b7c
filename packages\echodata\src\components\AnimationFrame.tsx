'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import type React from 'react';
import { useEffect, useRef } from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  exitVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
  isActive?: boolean;
  isExiting?: boolean;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  exitVariant,
  children,
  className,
  once = true,
  margin,
  isActive = false,
  isExiting = false,
}) => {
  const { isMobile } = useScreenSize();
  const elementRef = useRef<HTMLDivElement>(null);
  const { initial, animate, transition } = defaultAnimateConfig[variant];
  const exitConfig = exitVariant ? defaultAnimateConfig[exitVariant] : null;

  useEffect(() => {
    if (isMobile || !elementRef.current) return;

    const element = elementRef.current;

    console.log('AnimationFrame: Triggering animation', {
      variant,
      exitVariant,
      isActive,
      isExiting,
    });

    if (isExiting && exitConfig) {
      // Trigger exit animation using CSS transitions
      console.log('Playing exit animation:', exitVariant);

      // Set transition
      element.style.transition = `transform ${exitConfig.transition.duration || 0.8}s ${exitConfig.transition.ease || 'ease-in-out'} ${exitConfig.transition.delay || 0}s, opacity ${exitConfig.transition.duration || 0.8}s ${exitConfig.transition.ease || 'ease-in-out'} ${exitConfig.transition.delay || 0}s`;

      // Apply animation
      element.style.transform = `translateY(${exitConfig.animate.y || 0}px)`;
      element.style.opacity = String(exitConfig.animate.opacity || 1);
    } else if (isActive) {
      // Trigger enter animation using CSS transitions
      console.log('Playing enter animation:', variant);

      // Set initial state first
      element.style.transform = `translateY(${initial.y || 0}px)`;
      element.style.opacity = String(initial.opacity || 1);
      element.style.transition = 'none';

      // Force reflow
      element.offsetHeight;

      // Set transition
      element.style.transition = `transform ${transition.duration || 0.8}s ${transition.ease || 'ease-in-out'} ${transition.delay || 0}s, opacity ${transition.duration || 0.8}s ${transition.ease || 'ease-in-out'} ${transition.delay || 0}s`;

      // Apply animation
      element.style.transform = `translateY(${animate.y || 0}px)`;
      element.style.opacity = String(animate.opacity || 1);
    }
  }, [
    isActive,
    isExiting,
    variant,
    exitVariant,
    isMobile,
    initial,
    animate,
    transition,
    exitConfig,
  ]);

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <div
      ref={elementRef}
      className={clsx('hidden lg:flex', className)}
      style={{
        transform: `translateY(${initial.y || 0}px)`,
        opacity: Number(initial.opacity || 1),
      }}
    >
      {children}
    </div>
  );
};

export default AnimationFrame;

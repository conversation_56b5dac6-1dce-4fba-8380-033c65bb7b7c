'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import { motion } from 'motion/react';
import type React from 'react';
import { useEffect, useState } from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  exitVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
  isActive?: boolean;
  isExiting?: boolean;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  exitVariant,
  children,
  className,
  once = true,
  margin,
  isActive = false,
  isExiting = false,
}) => {
  const { isMobile } = useScreenSize();
  const { initial, animate, transition } = defaultAnimateConfig[variant];
  const [animationKey, setAnimationKey] = useState(0);

  // Use exit animation if exiting and exitVariant is provided
  const exitConfig = exitVariant ? defaultAnimateConfig[exitVariant] : null;

  // Force re-render when animation state changes
  useEffect(() => {
    setAnimationKey((prev) => prev + 1);
  }, [isActive, isExiting]);

  if (isExiting && exitConfig) {
    console.log('AnimationFrame: Using exit animation', exitVariant);
  }

  console.log(
    'AnimationFrame:',
    variant,
    'isActive:',
    isActive,
    'isExiting:',
    isExiting,
    'key:',
    animationKey,
  );

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <motion.div
      key={animationKey}
      initial={isExiting ? exitConfig?.initial || initial : initial}
      animate={
        isExiting
          ? exitConfig?.animate || animate
          : isActive
            ? animate
            : initial
      }
      transition={isExiting ? exitConfig?.transition || transition : transition}
      className={clsx('hidden lg:flex', className)}
    >
      {children}
    </motion.div>
  );
};

export default AnimationFrame;

'use client';

import type { VariantType } from '@hi7/configs/animation';
import { defaultAnimateConfig } from '@hi7/configs/animation';
import useScreenSize from '@hi7/helpers/useScreenSize';
import clsx from 'clsx';
import { motion } from 'motion/react';
import type React from 'react';

interface AnimationFrameProps {
  variant: VariantType;
  exitVariant?: VariantType;
  once?: boolean;
  className?: string;
  children: React.ReactNode;
  margin?: string;
  isActive?: boolean;
  isExiting?: boolean;
}

const AnimationFrame: React.FC<AnimationFrameProps> = ({
  variant,
  exitVariant,
  children,
  className,
  once = true,
  margin,
  isActive = false,
  isExiting = false,
}) => {
  const { isMobile } = useScreenSize();
  const { initial, animate, transition } = defaultAnimateConfig[variant];

  // Use exit animation if exiting and exitVariant is provided
  const exitConfig = exitVariant ? defaultAnimateConfig[exitVariant] : null;

  // Determine which animation to use
  let currentInitial = initial;
  let currentAnimate = animate;
  let currentTransition = transition;

  if (isExiting && exitConfig) {
    currentInitial = exitConfig.initial;
    currentAnimate = exitConfig.animate;
    currentTransition = exitConfig.transition;
  }

  return isMobile ? (
    <div className={clsx('lg:hidden', className)}>{children}</div>
  ) : (
    <motion.div
      initial={currentInitial}
      animate={isExiting ? currentAnimate : isActive ? animate : initial}
      viewport={{ once, margin }}
      transition={currentTransition}
      className={clsx('hidden lg:flex', className)}
    >
      {children}
    </motion.div>
  );
};

export default AnimationFrame;
